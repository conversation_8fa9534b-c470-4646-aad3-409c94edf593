#!/bin/bash

# 🚀 سكريبت النشر التلقائي لـ Hostinger
# تأكد من تحديث المتغيرات أدناه حسب إعدادات السيرفر الخاص بك

# ===== إعدادات السيرفر =====
SERVER_IP="your-server-ip"
SERVER_USER="your-username"
SERVER_PATH="/home/<USER>/public_html"
APP_NAME="droobhajer"

# ===== ألوان للعرض =====
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 بدء عملية النشر على Hostinger...${NC}"

# ===== التحقق من الملفات المطلوبة =====
echo -e "${YELLOW}📋 التحقق من الملفات المطلوبة...${NC}"

if [ ! -f ".env.production" ]; then
    echo -e "${RED}❌ ملف .env.production غير موجود!${NC}"
    echo -e "${YELLOW}💡 قم بنسخ .env.production.example وتحديث القيم${NC}"
    exit 1
fi

if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ ملف package.json غير موجود!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ جميع الملفات المطلوبة موجودة${NC}"

# ===== بناء المشروع محلياً =====
echo -e "${YELLOW}🔨 بناء المشروع محلياً...${NC}"

# تنظيف البناء السابق
rm -rf .next
rm -rf node_modules

# تثبيت Dependencies
npm install

# بناء المشروع
npm run build

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ فشل في بناء المشروع!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ تم بناء المشروع بنجاح${NC}"

# ===== إنشاء أرشيف للنشر =====
echo -e "${YELLOW}📦 إنشاء أرشيف للنشر...${NC}"

# إنشاء مجلد مؤقت
mkdir -p deploy_temp

# نسخ الملفات المطلوبة
cp -r .next deploy_temp/
cp -r public deploy_temp/
cp -r styles deploy_temp/
cp package.json deploy_temp/
cp package-lock.json deploy_temp/
cp next.config.js deploy_temp/
cp .env.production deploy_temp/.env.local
cp ecosystem.config.js deploy_temp/
cp .htaccess deploy_temp/

# إنشاء مجلد logs
mkdir -p deploy_temp/logs

# ضغط الملفات
tar -czf deploy.tar.gz -C deploy_temp .

# تنظيف المجلد المؤقت
rm -rf deploy_temp

echo -e "${GREEN}✅ تم إنشاء أرشيف النشر: deploy.tar.gz${NC}"

# ===== رفع الملفات إلى السيرفر =====
echo -e "${YELLOW}📤 رفع الملفات إلى السيرفر...${NC}"

# رفع الأرشيف
scp deploy.tar.gz $SERVER_USER@$SERVER_IP:$SERVER_PATH/

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ فشل في رفع الملفات!${NC}"
    echo -e "${YELLOW}💡 تأكد من إعدادات SSH والاتصال بالسيرفر${NC}"
    exit 1
fi

echo -e "${GREEN}✅ تم رفع الملفات بنجاح${NC}"

# ===== تثبيت وتشغيل على السيرفر =====
echo -e "${YELLOW}⚙️ تثبيت وتشغيل على السيرفر...${NC}"

ssh $SERVER_USER@$SERVER_IP << EOF
    cd $SERVER_PATH
    
    # إيقاف التطبيق إذا كان يعمل
    pm2 stop $APP_NAME 2>/dev/null || true
    
    # استخراج الملفات
    tar -xzf deploy.tar.gz
    
    # تثبيت Dependencies
    npm install --production
    
    # تشغيل التطبيق
    pm2 start ecosystem.config.js --env production
    pm2 save
    
    # تنظيف الأرشيف
    rm deploy.tar.gz
    
    echo "✅ تم النشر بنجاح!"
EOF

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ فشل في تثبيت أو تشغيل التطبيق على السيرفر!${NC}"
    exit 1
fi

# ===== تنظيف الملفات المحلية =====
rm deploy.tar.gz

echo -e "${GREEN}🎉 تم النشر بنجاح!${NC}"
echo -e "${BLUE}🌐 يمكنك الآن زيارة موقعك على: https://your-domain.com${NC}"
echo -e "${YELLOW}📊 لمراقبة التطبيق: ssh $SERVER_USER@$SERVER_IP 'pm2 status'${NC}"

# ===== اختبار سريع =====
echo -e "${YELLOW}🧪 اختبار سريع للموقع...${NC}"
sleep 5

# اختبار الاتصال (يحتاج curl)
if command -v curl &> /dev/null; then
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://your-domain.com)
    if [ $HTTP_STATUS -eq 200 ]; then
        echo -e "${GREEN}✅ الموقع يعمل بشكل طبيعي (HTTP 200)${NC}"
    else
        echo -e "${YELLOW}⚠️ الموقع يستجيب بكود: $HTTP_STATUS${NC}"
    fi
else
    echo -e "${YELLOW}💡 قم بتثبيت curl لاختبار الموقع تلقائياً${NC}"
fi

echo -e "${BLUE}🔧 للمزيد من المساعدة، راجع: HOSTINGER_DEPLOYMENT_GUIDE.md${NC}"

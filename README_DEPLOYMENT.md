# 🚀 دروب هاجر - دليل النشر السريع

## 📊 ملخص المشروع:
- **الموقع**: http://droobhajer.com
- **السيرفر**: **************
- **المستخدم**: wahid.alduais
- **قاعدة البيانات**: droobhajer_db

---

## ⚡ النشر في 3 خطوات:

### 1️⃣ تحضير المشروع محلياً:
```bash
chmod +x setup-production.sh
./setup-production.sh
```

### 2️⃣ رفع الملفات:
- ارفع `droobhajer-production.tar.gz` إلى Hostinger
- مسار الرفع: `/home/<USER>/public_html/`

### 3️⃣ تشغيل على السيرفر:
```bash
cd /home/<USER>/public_html/
tar -xzf droobhajer-production.tar.gz
npm install --production
pm2 start ecosystem.config.js --env production
```

---

## 🔗 روابط مهمة:

| الرابط | الوصف |
|--------|--------|
| http://droobhajer.com | الموقع الرئيسي |
| http://droobhajer.com/admin | لوحة الإدارة |
| http://droobhajer.com/api/test-db-connection | اختبار قاعدة البيانات |

---

## 🔑 بيانات تسجيل الدخول:

### لوحة الإدارة:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `DroobHajer@2024!ProductionAdmin#Secure`

⚠️ **مهم**: غيّر كلمة المرور بعد أول تسجيل دخول!

---

## 🛠️ أوامر مفيدة:

### مراقبة التطبيق:
```bash
pm2 status          # حالة التطبيق
pm2 logs droobhajer  # سجلات التطبيق
pm2 restart droobhajer # إعادة تشغيل
```

### قاعدة البيانات:
```bash
# إضافة كلمة مرور لقاعدة البيانات
chmod +x update-db-password.sh
./update-db-password.sh
```

### النسخ الاحتياطية:
```bash
# نسخة احتياطية من قاعدة البيانات
mysqldump -u root -p droobhajer_db > backup_$(date +%Y%m%d).sql
```

---

## 📁 ملفات الإعداد المهمة:

| الملف | الوصف |
|-------|--------|
| `.env.local` | إعدادات الإنتاج |
| `ecosystem.config.js` | إعدادات PM2 |
| `database-setup-production.sql` | إعداد قاعدة البيانات |
| `DROOBHAJER_DEPLOYMENT.md` | دليل النشر التفصيلي |

---

## 🆘 استكشاف الأخطاء:

### المشكلة: التطبيق لا يعمل
```bash
pm2 logs droobhajer --lines 50
```

### المشكلة: خطأ في قاعدة البيانات
```bash
mysql -u root -p droobhajer_db
SHOW TABLES;
```

### المشكلة: خطأ في الملفات
```bash
ls -la /home/<USER>/public_html/
```

---

## 📞 للمساعدة:

راجع الملفات التالية للحصول على تفاصيل أكثر:
- `HOSTINGER_DEPLOYMENT_GUIDE.md`
- `QUICK_DEPLOYMENT_STEPS.md`
- `DROOBHAJER_DEPLOYMENT.md`

---

**🎉 مبروك! مشروع دروب هاجر جاهز للنشر!**

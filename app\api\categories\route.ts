import { NextRequest, NextResponse } from 'next/server';
import { unstable_cache } from 'next/cache';
import { getCategories, addCategory, updateCategory, deleteCategory, getCategoryById } from '@/lib/mysql-database';
import { handleCacheInvalidation } from '@/lib/cache-manager';
import { v4 as uuidv4 } from 'uuid';

// إنشاء cached versions للفئات
const getCachedCategories = unstable_cache(
  async () => getCategories(),
  ['categories-all'],
  { revalidate: 3600, tags: ['categories'] }
);

const getCachedCategoryById = unstable_cache(
  async (id: string) => getCategoryById(id),
  ['category-by-id'],
  { revalidate: 3600, tags: ['categories'] }
);

// GET - جلب جميع الفئات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    // إذا تم تمرير ID محدد، جلب فئة واحدة
    if (id && typeof id === 'string') {
      const category = await getCachedCategoryById(id);
      if (!category) {
        return NextResponse.json({
          success: false,
          message: 'Category not found',
          messageAr: 'الفئة غير موجودة'
        }, { status: 404 });
      }
      return NextResponse.json({
        success: true,
        data: category
      }, {
        headers: {
          'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200'
        }
      });
    }

    // جلب جميع الفئات مع Cache
    const categories = await getCachedCategories();
    return NextResponse.json({
      success: true,
      data: categories
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200'
      }
    });
  } catch (error) {
    console.error('Categories GET API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// POST - إضافة فئة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, nameAr, description, descriptionAr, image, isActive } = body;

    if (!name || !nameAr) {
      return NextResponse.json({
        success: false,
        message: 'Name and Arabic name are required',
        messageAr: 'الاسم والاسم بالعربية مطلوبان'
      }, { status: 400 });
    }

    const categoryData = {
      id: uuidv4(),
      name,
      name_ar: nameAr,
      description: description || null,
      description_ar: descriptionAr || null,
      image_url: image || null,
      is_active: isActive !== undefined ? isActive : true
    };

    const newCategory = await addCategory(categoryData);

    // إعادة التحقق من الكاش بعد إضافة فئة جديدة
    handleCacheInvalidation('create', 'category');

    return NextResponse.json({
      success: true,
      data: newCategory,
      message: 'Category created successfully',
      messageAr: 'تم إنشاء الفئة بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Categories POST API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// PUT - تحديث فئة
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const updateId = searchParams.get('id');

    if (!updateId || typeof updateId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Category ID is required',
        messageAr: 'معرف الفئة مطلوب'
      }, { status: 400 });
    }

    const existingCategory = await getCategoryById(updateId);
    if (!existingCategory) {
      return NextResponse.json({
        success: false,
        message: 'Category not found',
        messageAr: 'الفئة غير موجودة'
      }, { status: 404 });
    }

    const body = await request.json();
    const { name, nameAr, description, descriptionAr, image, isActive } = body;

    // تحويل أسماء الحقول لتتطابق مع قاعدة البيانات
    const updateData = {
      name,
      name_ar: nameAr,
      description,
      description_ar: descriptionAr,
      image_url: image,
      is_active: isActive
    };

    const updatedCategory = await updateCategory(updateId, updateData);

    // إعادة التحقق من الكاش بعد تحديث الفئة
    handleCacheInvalidation('update', 'category');

    return NextResponse.json({
      success: true,
      data: updatedCategory,
      message: 'Category updated successfully',
      messageAr: 'تم تحديث الفئة بنجاح'
    });
  } catch (error) {
    console.error('Categories PUT API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// DELETE - حذف فئة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const deleteId = searchParams.get('id');

    if (!deleteId || typeof deleteId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Category ID is required',
        messageAr: 'معرف الفئة مطلوب'
      }, { status: 400 });
    }

    const deleted = await deleteCategory(deleteId);

    if (!deleted) {
      return NextResponse.json({
        success: false,
        message: 'Category not found',
        messageAr: 'الفئة غير موجودة'
      }, { status: 404 });
    }

    // إعادة التحقق من الكاش بعد حذف الفئة
    handleCacheInvalidation('delete', 'category');

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully',
      messageAr: 'تم حذف الفئة بنجاح'
    });
  } catch (error) {
    console.error('Categories DELETE API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

'use client';

import { useEffect } from 'react';
import { initializePerformanceOptimizations } from '../lib/performance-optimizer';

/**
 * مكون لتهيئة تحسينات الأداء
 */
const PerformanceInitializer: React.FC = () => {
  useEffect(() => {
    // تهيئة تحسينات الأداء عند تحميل التطبيق
    initializePerformanceOptimizations();

    // تحسين الخطوط
    if (typeof window !== 'undefined') {
      // تحميل مسبق للخطوط المهمة
      const fontPreloads = [
        '/fonts/inter-var.woff2',
        '/fonts/arabic-font.woff2'
      ];

      fontPreloads.forEach(font => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = font;
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });

      // تحسين التمرير السلس
      document.documentElement.style.scrollBehavior = 'smooth';

      // تحسين focus للوصولية
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
          document.body.classList.add('keyboard-navigation');
        }
      });

      document.addEventListener('mousedown', () => {
        document.body.classList.remove('keyboard-navigation');
      });

      // تحسين الصور الكسولة
      const images = document.querySelectorAll('img[loading="lazy"]');
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
              }
            }
          });
        });

        images.forEach((img) => imageObserver.observe(img));
      }

      // تحسين الذاكرة - تنظيف عند إغلاق الصفحة
      const cleanup = () => {
        // تنظيف event listeners
        // مسح timers
        // تنظيف caches
      };

      window.addEventListener('beforeunload', cleanup);
      
      return () => {
        window.removeEventListener('beforeunload', cleanup);
      };
    }
  }, []);

  // هذا المكون لا يعرض أي شيء - فقط يقوم بالتهيئة
  return null;
};

export default PerformanceInitializer;

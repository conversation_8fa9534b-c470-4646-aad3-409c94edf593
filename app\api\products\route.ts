import { NextRequest, NextResponse } from 'next/server';
import { unstable_cache } from 'next/cache';
import {
  getProductsWithDetails,
  getFeaturedProductsWithDetails,
  getProductsByCategoryWithDetails,
  getProductsBySubcategoryWithDetails,
  getProductWithDetails
} from '@/lib/mysql-database';
import { ProductWithDetails } from '@/types/mysql-database';

// إنشاء cached versions للدوال
const getCachedProducts = unstable_cache(
  async () => getProductsWithDetails(),
  ['products-all'],
  { revalidate: 1800, tags: ['products'] }
);

const getCachedFeaturedProducts = unstable_cache(
  async () => getFeaturedProductsWithDetails(),
  ['products-featured'],
  { revalidate: 900, tags: ['products', 'featured'] }
);

const getCachedProductsByCategory = unstable_cache(
  async (categoryId: string) => getProductsByCategoryWithDetails(categoryId),
  ['products-by-category'],
  { revalidate: 1800, tags: ['products', 'categories'] }
);

const getCachedProductsBySubcategory = unstable_cache(
  async (subcategoryId: string) => getProductsBySubcategoryWithDetails(subcategoryId),
  ['products-by-subcategory'],
  { revalidate: 1800, tags: ['products', 'subcategories'] }
);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const featured = searchParams.get('featured');
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');
    const id = searchParams.get('id');

    let products: ProductWithDetails[];

    // إذا تم تمرير ID محدد، جلب منتج واحد مع التفاصيل
    if (id && typeof id === 'string') {
      const product = await getProductWithDetails(id);
      if (!product) {
        return NextResponse.json({
          success: false,
          message: 'Product not found',
          messageAr: 'المنتج غير موجود'
        }, { status: 404 });
      }
      return NextResponse.json({ success: true, data: product });
    }

    // جلب المنتجات حسب الفلترة مع استخدام Cache
    if (featured === 'true') {
      products = await getCachedFeaturedProducts();
    } else if (categoryId && typeof categoryId === 'string') {
      products = await getCachedProductsByCategory(categoryId);
    } else if (subcategoryId && typeof subcategoryId === 'string') {
      products = await getCachedProductsBySubcategory(subcategoryId);
    } else {
      products = await getCachedProducts();
    }

    return NextResponse.json({
      success: true,
      data: products
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600',
        'CDN-Cache-Control': 'public, s-maxage=1800',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=1800'
      }
    });
  } catch (error) {
    console.error('Products API Error:', error);
    return NextResponse.json({ 
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
